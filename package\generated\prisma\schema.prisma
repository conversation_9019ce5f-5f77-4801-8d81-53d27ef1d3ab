// MOKO SOSTANZA Dental CRM - Prisma Schema
// Database schema per gestionale dentisti con PostgreSQL
// Supporta: Pazienti, Appuntamenti, Fatture, File, Notifiche, UDI tracking

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// MODELLI PRINCIPALI
// ================================

/// Tabella pazienti - Informazioni complete del paziente
model Patient {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // Dati anagrafici
  firstName   String    @db.VarChar(100)
  lastName    String    @db.VarChar(100)
  email       String?   @unique @db.VarChar(255)
  phone       String?   @db.VarChar(20)
  dateOfBirth DateTime? @db.Date
  fiscalCode  String?   @unique @db.VarChar(16)

  // Indirizzo
  address    String? @db.VarChar(255)
  city       String? @db.VarChar(100)
  postalCode String? @db.VarChar(10)
  province   String? @db.VarChar(50)

  // Dati medici
  medicalHistory String? @db.Text
  allergies      String? @db.Text
  medications    String? @db.Text
  isSmoker       Boolean @default(false)
  anamnesis      String? @db.Text

  // Relazioni
  appointments  Appointment[]
  invoices      Invoice[]
  files         File[]
  patientUDIs   PatientUDI[]
  notifications Notification[]

  @@index([email])
  @@index([fiscalCode])
  @@index([lastName, firstName])
  @@map("patients")
}

/// Tabella UDI - Dispositivi medici con codice UDI per tracciabilità
model UDI {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // Dati UDI
  udiCode        String    @unique @db.VarChar(100)
  deviceName     String    @db.VarChar(255)
  manufacturer   String    @db.VarChar(255)
  lotNumber      String?   @db.VarChar(100)
  serialNumber   String?   @db.VarChar(100)
  expirationDate DateTime? @db.Date

  // Descrizione e note
  description String? @db.Text
  notes       String? @db.Text

  // Relazioni
  patientUDIs PatientUDI[]

  @@index([udiCode])
  @@index([manufacturer])
  @@map("udis")
}

/// Tabella di join Patient-UDI per tracciare dispositivi utilizzati per paziente
model PatientUDI {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz

  // Relazioni
  patientId String  @db.Uuid
  udiId     String  @db.Uuid
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
  udi       UDI     @relation(fields: [udiId], references: [id], onDelete: Cascade)

  // Dati dell'intervento
  interventionDate DateTime @db.Timestamptz
  interventionType String   @db.VarChar(100) // "chirurgico" | "non_chirurgico"
  teethInvolved    String?  @db.Text // JSON array dei denti coinvolti
  notes            String?  @db.Text

  @@unique([patientId, udiId, interventionDate])
  @@index([patientId])
  @@index([udiId])
  @@index([interventionDate])
  @@map("patient_udis")
}

/// Tabella appuntamenti - Gestione calendario e prenotazioni
model Appointment {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // Dati appuntamento
  title       String   @db.VarChar(255)
  description String?  @db.Text
  startTime   DateTime @db.Timestamptz
  endTime     DateTime @db.Timestamptz
  status      String   @default("scheduled") @db.VarChar(50) // "scheduled" | "confirmed" | "completed" | "cancelled"

  // Tipo e priorità
  appointmentType String @db.VarChar(100) // "visita" | "controllo" | "intervento" | "consulenza"
  priority        String @default("normal") @db.VarChar(20) // "low" | "normal" | "high" | "urgent"

  // Relazioni
  patientId String  @db.Uuid
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)

  // Dati aggiuntivi
  notes        String? @db.Text
  reminderSent Boolean @default(false)

  @@index([patientId])
  @@index([startTime])
  @@index([status])
  @@index([appointmentType])
  @@map("appointments")
}

/// Tabella fatture - Gestione fatturazione e pagamenti
model Invoice {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // Dati fattura
  invoiceNumber String   @unique @db.VarChar(50)
  issueDate     DateTime @db.Date
  dueDate       DateTime @db.Date

  // Importi
  subtotal  Decimal @db.Decimal(10, 2)
  taxRate   Decimal @default(22.00) @db.Decimal(5, 2)
  taxAmount Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)

  // Status e pagamento
  status        String    @default("draft") @db.VarChar(50) // "draft" | "sent" | "paid" | "overdue" | "cancelled"
  paymentMethod String?   @db.VarChar(50) // "cash" | "card" | "transfer" | "check"
  paymentDate   DateTime? @db.Date

  // Relazioni
  patientId String  @db.Uuid
  patient   Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)

  // Dati aggiuntivi
  description String? @db.Text
  notes       String? @db.Text

  @@index([patientId])
  @@index([invoiceNumber])
  @@index([status])
  @@index([issueDate])
  @@index([dueDate])
  @@map("invoices")
}

/// Tabella file - Gestione documenti e allegati
model File {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // Dati file
  fileName     String @db.VarChar(255)
  originalName String @db.VarChar(255)
  mimeType     String @db.VarChar(100)
  fileSize     Int // Size in bytes
  filePath     String @db.VarChar(500)

  // Categorizzazione
  fileType    String  @db.VarChar(50) // "document" | "image" | "xray" | "report" | "prescription"
  category    String? @db.VarChar(100)
  description String? @db.Text

  // Relazioni
  patientId String?  @db.Uuid
  patient   Patient? @relation(fields: [patientId], references: [id], onDelete: Cascade)

  // Metadati
  isPublic Boolean @default(false)
  tags     String? @db.Text // JSON array di tag

  @@index([patientId])
  @@index([fileType])
  @@index([createdAt])
  @@map("files")
}

/// Tabella notifiche - Sistema di notifiche e promemoria
model Notification {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz
  updatedAt DateTime @updatedAt @db.Timestamptz

  // Dati notifica
  title    String @db.VarChar(255)
  message  String @db.Text
  type     String @db.VarChar(50) // "appointment" | "payment" | "reminder" | "system" | "alert"
  priority String @default("normal") @db.VarChar(20) // "low" | "normal" | "high" | "urgent"

  // Status
  isRead     Boolean @default(false)
  isArchived Boolean @default(false)

  // Scheduling
  scheduledFor DateTime? @db.Timestamptz
  sentAt       DateTime? @db.Timestamptz

  // Relazioni
  patientId String?  @db.Uuid
  patient   Patient? @relation(fields: [patientId], references: [id], onDelete: Cascade)

  // Metadati
  metadata String? @db.Text // JSON per dati aggiuntivi

  @@index([patientId])
  @@index([type])
  @@index([isRead])
  @@index([scheduledFor])
  @@index([createdAt])
  @@map("notifications")
}
