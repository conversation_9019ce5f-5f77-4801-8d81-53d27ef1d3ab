{"name": "matdash-react-vite-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "db:migrate": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@svgr/rollup": "8.1.0", "@tabler/icons-react": "^2.39.0", "@types/react-helmet-async": "^1.0.1", "aos": "^2.3.4", "apexcharts": "3.48.0", "chance": "^1.1.11", "date-fns": "^2.30.0", "flowbite": "^2.5.2", "flowbite-react": "^0.10.2", "formik": "^2.4.5", "formik-mui": "^5.0.0-alpha.0", "gray-matter": "^4.0.3", "html2canvas": "^1.4.1", "i18next": "^23.5.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "next-auth": "^4.24.11", "prop-types": "^15.7.2", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-intersection-observer": "^9.5.2", "react-router": "^7.0.2", "react-router-dom": "^7.0.2", "remark": "^15.0.1", "remark-html": "^16.0.1", "simplebar": "^6.2.7", "simplebar-react": "^3.2.4", "stylis-plugin-rtl": "^2.1.1", "yup": "^0.32.11", "zustand": "^5.0.3"}, "devDependencies": {"@iconify/react": "^5.1.0", "@types/aos": "^3.0.7", "@types/chance": "^1.1.6", "@types/node": "20.10.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "latest", "eslint-config-next": "latest", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5.3.3", "vite": "^5.0.12"}}