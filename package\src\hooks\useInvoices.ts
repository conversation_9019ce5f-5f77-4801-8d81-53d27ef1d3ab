/**
 * Hook personalizzato per la gestione delle fatture
 * Fornisce funzionalità CRUD complete con gestione stato e errori
 */

import { useState, useEffect, useCallback } from 'react';
import { useGlobalStore } from '../store/useGlobalStore';
import { 
  IInvoice, 
  ICreateInvoice, 
  IUpdateInvoice, 
  IInvoiceFilters,
  IInvoiceStats,
  InvoiceStatus,
  getStatusLabel,
  getStatusColor,
  formatCurrency,
  formatInvoiceDate,
  generateInvoiceNumber,
  calculateTotalWithTax,
  isInvoiceOverdue
} from '../types/invoices/IInvoice';

// Mock data per lo sviluppo locale
const generateMockInvoices = (): IInvoice[] => {
  const invoices: IInvoice[] = [];
  const statuses = Object.values(InvoiceStatus);
  
  for (let i = 0; i < 15; i++) {
    const issueDate = new Date();
    issueDate.setDate(issueDate.getDate() - Math.floor(Math.random() * 90));
    
    const dueDate = new Date(issueDate);
    dueDate.setDate(dueDate.getDate() + 30);
    
    const amount = Math.floor(Math.random() * 1000) + 100;
    const { taxAmount, totalAmount } = calculateTotalWithTax(amount, 22);
    
    invoices.push({
      id: `inv_${i + 1}`,
      patientId: `pat_${Math.floor(Math.random() * 3) + 1}`,
      invoiceNumber: generateInvoiceNumber(issueDate.getFullYear()),
      issueDate,
      dueDate,
      amount,
      taxAmount,
      totalAmount,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      description: `Trattamento odontoiatrico ${i + 1}`,
      notes: Math.random() > 0.7 ? 'Note aggiuntive per questa fattura' : undefined,
      createdAt: new Date(issueDate.getTime() - Math.random() * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
      patient: {
        id: `pat_${Math.floor(Math.random() * 3) + 1}`,
        firstName: ['Mario', 'Giulia', 'Luca'][Math.floor(Math.random() * 3)],
        lastName: ['Rossi', 'Bianchi', 'Verdi'][Math.floor(Math.random() * 3)],
        fiscalCode: '****************',
        email: '<EMAIL>',
        phone: '+39 ***********'
      }
    });
  }
  
  return invoices.sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
};

interface UseInvoicesOptions {
  autoLoad?: boolean;
  filters?: IInvoiceFilters;
}

export const useInvoices = (options: UseInvoicesOptions = {}) => {
  const { autoLoad = true, filters } = options;
  
  // Stato locale
  const [invoices, setInvoices] = useState<IInvoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<IInvoice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Store globale
  const { 
    isOnline, 
    showNotification,
    incrementVersion 
  } = useGlobalStore();

  // Simula chiamata API per caricare fatture
  const loadInvoices = useCallback(async (): Promise<IInvoice[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // In produzione, qui ci sarà la chiamata API reale
      const mockData = generateMockInvoices();
      setInvoices(mockData);
      
      console.log('✅ Fatture caricate:', mockData.length);
      return mockData;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nel caricamento delle fatture';
      setError(errorMessage);
      console.error('❌ Errore caricamento fatture:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [isOnline]);

  // Crea una nuova fattura
  const createInvoice = useCallback(async (invoiceData: ICreateInvoice): Promise<IInvoice | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // Genera numero fattura se non fornito
      const invoiceNumber = invoiceData.invoiceNumber || generateInvoiceNumber();
      
      // Calcola importi se non forniti
      const { taxAmount, totalAmount } = calculateTotalWithTax(invoiceData.amount, 22);
      
      // Crea nuova fattura
      const newInvoice: IInvoice = {
        id: `inv_${Date.now()}`,
        ...invoiceData,
        invoiceNumber,
        taxAmount: invoiceData.taxAmount || taxAmount,
        totalAmount: totalAmount,
        status: invoiceData.status || InvoiceStatus.DRAFT,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Aggiorna lo stato locale
      setInvoices(prev => [newInvoice, ...prev]);
      
      incrementVersion();
      showNotification('Fattura creata con successo', 'success');
      
      console.log('✅ Fattura creata:', newInvoice);
      return newInvoice;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nella creazione della fattura';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
      console.error('❌ Errore creazione fattura:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isOnline, incrementVersion, showNotification]);

  // Aggiorna una fattura esistente
  const updateInvoice = useCallback(async (id: string, invoiceData: IUpdateInvoice): Promise<IInvoice | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // Trova e aggiorna la fattura
      const updatedInvoices = invoices.map(invoice => {
        if (invoice.id === id) {
          const updatedInvoice = { ...invoice, ...invoiceData, updatedAt: new Date() };
          
          // Ricalcola totali se l'importo è cambiato
          if (invoiceData.amount && invoiceData.amount !== invoice.amount) {
            const { taxAmount, totalAmount } = calculateTotalWithTax(invoiceData.amount, 22);
            updatedInvoice.taxAmount = taxAmount;
            updatedInvoice.totalAmount = totalAmount;
          }
          
          return updatedInvoice;
        }
        return invoice;
      });
      
      const updatedInvoice = updatedInvoices.find(i => i.id === id);
      
      if (!updatedInvoice) {
        throw new Error('Fattura non trovata');
      }
      
      setInvoices(updatedInvoices);
      
      incrementVersion();
      showNotification('Fattura aggiornata con successo', 'success');
      
      console.log('✅ Fattura aggiornata:', updatedInvoice);
      return updatedInvoice;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'aggiornamento della fattura';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
      console.error('❌ Errore aggiornamento fattura:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [invoices, isOnline, incrementVersion, showNotification]);

  // Elimina una fattura
  const deleteInvoice = useCallback(async (id: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // Rimuovi la fattura
      setInvoices(prev => prev.filter(invoice => invoice.id !== id));
      
      incrementVersion();
      showNotification('Fattura eliminata con successo', 'success');
      
      console.log('✅ Fattura eliminata:', id);
      return true;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'eliminazione della fattura';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
      console.error('❌ Errore eliminazione fattura:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isOnline, incrementVersion, showNotification]);

  // Caricamento iniziale
  useEffect(() => {
    if (autoLoad && invoices.length === 0) {
      loadInvoices();
    }
  }, [autoLoad, invoices.length, loadInvoices]);

  // Applicazione filtri
  useEffect(() => {
    let filtered = [...invoices];
    
    if (filters) {
      if (filters.patientId) {
        filtered = filtered.filter(inv => inv.patientId === filters.patientId);
      }
      
      if (filters.status) {
        filtered = filtered.filter(inv => inv.status === filters.status);
      }
      
      if (filters.startDate) {
        filtered = filtered.filter(inv => inv.issueDate >= filters.startDate!);
      }
      
      if (filters.endDate) {
        filtered = filtered.filter(inv => inv.issueDate <= filters.endDate!);
      }
      
      if (filters.minAmount) {
        filtered = filtered.filter(inv => inv.totalAmount >= filters.minAmount!);
      }
      
      if (filters.maxAmount) {
        filtered = filtered.filter(inv => inv.totalAmount <= filters.maxAmount!);
      }
      
      if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        filtered = filtered.filter(inv => 
          inv.invoiceNumber.toLowerCase().includes(term) ||
          inv.description?.toLowerCase().includes(term) ||
          inv.patient?.firstName.toLowerCase().includes(term) ||
          inv.patient?.lastName.toLowerCase().includes(term) ||
          inv.notes?.toLowerCase().includes(term)
        );
      }
    }
    
    setFilteredInvoices(filtered);
  }, [invoices, filters]);

  // Utility functions
  const findById = useCallback((id: string): IInvoice | undefined => {
    return invoices.find(invoice => invoice.id === id);
  }, [invoices]);

  const getInvoicesByPatient = useCallback((patientId: string): IInvoice[] => {
    return invoices.filter(inv => inv.patientId === patientId);
  }, [invoices]);

  const getStats = useCallback((): IInvoiceStats => {
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentYear = new Date(now.getFullYear(), 0, 1);
    
    return {
      total: invoices.length,
      draft: invoices.filter(i => i.status === InvoiceStatus.DRAFT).length,
      sent: invoices.filter(i => i.status === InvoiceStatus.SENT).length,
      paid: invoices.filter(i => i.status === InvoiceStatus.PAID).length,
      overdue: invoices.filter(i => i.status === InvoiceStatus.OVERDUE || isInvoiceOverdue(i)).length,
      cancelled: invoices.filter(i => i.status === InvoiceStatus.CANCELLED).length,
      totalAmount: invoices.reduce((sum, i) => sum + i.totalAmount, 0),
      paidAmount: invoices.filter(i => i.status === InvoiceStatus.PAID).reduce((sum, i) => sum + i.totalAmount, 0),
      pendingAmount: invoices.filter(i => i.status === InvoiceStatus.SENT).reduce((sum, i) => sum + i.totalAmount, 0),
      overdueAmount: invoices.filter(i => i.status === InvoiceStatus.OVERDUE || isInvoiceOverdue(i)).reduce((sum, i) => sum + i.totalAmount, 0),
      monthlyTotal: invoices.filter(i => i.issueDate >= currentMonth).reduce((sum, i) => sum + i.totalAmount, 0),
      yearlyTotal: invoices.filter(i => i.issueDate >= currentYear).reduce((sum, i) => sum + i.totalAmount, 0),
    };
  }, [invoices]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refresh = useCallback(() => {
    return loadInvoices();
  }, [loadInvoices]);

  return {
    // Dati
    invoices: filteredInvoices.length > 0 ? filteredInvoices : invoices,
    allInvoices: invoices,
    isLoading,
    error,
    
    // Azioni CRUD
    createInvoice,
    updateInvoice,
    deleteInvoice,
    
    // Utilità
    refresh,
    findById,
    getInvoicesByPatient,
    getStats,
    clearError,
    
    // Stato
    hasData: invoices.length > 0,
    isEmpty: invoices.length === 0,
    
    // Helper per UI
    getStatusLabel,
    getStatusColor,
    formatCurrency,
    formatInvoiceDate
  };
};
