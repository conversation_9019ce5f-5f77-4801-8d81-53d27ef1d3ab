/**
 * Hook personalizzato per la gestione dei pazienti
 * 
 * <PERSON>o hook fornisce un'interfaccia semplice per interagire con i pazienti
 * nel database, utilizzando lo store Zustand e i servizi Prisma.
 * 
 * Caratteristiche:
 * - Caricamento automatico dei pazienti
 * - Operazioni CRUD semplificate
 * - Gestione dello stato di caricamento e errori
 * - Compatibilità con componenti React esistenti
 */

import { useEffect } from 'react';
import { useGlobalStore } from '../store/useGlobalStore';
import { IPatient, ICreatePatient } from '../types/patients/IPatient';

/**
 * Hook per la gestione dei pazienti
 * @param autoLoad Se caricare automaticamente i pazienti all'inizializzazione (default: true)
 * @returns Oggetto con dati e funzioni per gestire i pazienti
 */
export const usePatients = (autoLoad: boolean = true) => {
  // Estrai le funzioni e i dati dallo store con tipizzazione corretta
  const patients = useGlobalStore((state: any) => state.patients) as IPatient[];
  const isLoading = useGlobalStore((state: any) => state.isLoading) as boolean;
  const error = useGlobalStore((state: any) => state.error) as string | null;
  const loadPatients = useGlobalStore((state: any) => state.loadPatients) as () => Promise<void>;
  const createPatient = useGlobalStore((state: any) => state.createPatient) as (data: ICreatePatient) => Promise<IPatient | null>;
  const updatePatient = useGlobalStore((state: any) => state.updatePatient) as (id: string, data: Partial<ICreatePatient>) => Promise<IPatient | null>;
  const deletePatient = useGlobalStore((state: any) => state.deletePatient) as (id: string) => Promise<boolean>;
  const searchPatients = useGlobalStore((state: any) => state.searchPatients) as (query: string) => Promise<IPatient[]>;
  const clearError = useGlobalStore((state: any) => state.clearError) as () => void;

  // Carica i pazienti automaticamente se richiesto
  useEffect(() => {
    if (autoLoad && patients.length === 0 && !isLoading) {
      loadPatients();
    }
  }, [autoLoad, patients.length, isLoading, loadPatients]);

  /**
   * Crea un nuovo paziente
   * @param patientData Dati del paziente da creare
   * @returns Promise con il paziente creato o null in caso di errore
   */
  const handleCreatePatient = async (patientData: ICreatePatient): Promise<IPatient | null> => {
    try {
      const result = await createPatient(patientData);
      if (result) {
        console.log('✅ Paziente creato con successo nel hook:', result.id);
      }
      return result;
    } catch (error) {
      console.error('❌ Errore creazione paziente nel hook:', error);
      return null;
    }
  };

  /**
   * Aggiorna un paziente esistente
   * @param id ID del paziente da aggiornare
   * @param patientData Dati da aggiornare
   * @returns Promise con il paziente aggiornato o null in caso di errore
   */
  const handleUpdatePatient = async (
    id: string, 
    patientData: Partial<ICreatePatient>
  ): Promise<IPatient | null> => {
    try {
      const result = await updatePatient(id, patientData);
      if (result) {
        console.log('✅ Paziente aggiornato con successo nel hook:', result.id);
      }
      return result;
    } catch (error) {
      console.error('❌ Errore aggiornamento paziente nel hook:', error);
      return null;
    }
  };

  /**
   * Elimina un paziente
   * @param id ID del paziente da eliminare
   * @returns Promise con true se eliminato con successo, false altrimenti
   */
  const handleDeletePatient = async (id: string): Promise<boolean> => {
    try {
      const result = await deletePatient(id);
      if (result) {
        console.log('✅ Paziente eliminato con successo nel hook:', id);
      }
      return result;
    } catch (error) {
      console.error('❌ Errore eliminazione paziente nel hook:', error);
      return false;
    }
  };

  /**
   * Cerca pazienti per query
   * @param query Stringa di ricerca
   * @returns Promise con array di pazienti trovati
   */
  const handleSearchPatients = async (query: string): Promise<IPatient[]> => {
    try {
      const results = await searchPatients(query);
      console.log(`✅ Trovati ${results.length} pazienti per query: "${query}"`);
      return results;
    } catch (error) {
      console.error('❌ Errore ricerca pazienti nel hook:', error);
      return [];
    }
  };

  /**
   * Ricarica la lista dei pazienti
   */
  const handleRefresh = async (): Promise<void> => {
    try {
      await loadPatients();
      console.log('✅ Lista pazienti ricaricata con successo');
    } catch (error) {
      console.error('❌ Errore ricaricamento pazienti nel hook:', error);
    }
  };

  /**
   * Trova un paziente per ID
   * @param id ID del paziente da trovare
   * @returns Paziente trovato o undefined
   */
  const findPatientById = (id: string): IPatient | undefined => {
    return patients.find((patient: IPatient) => patient.id === id);
  };

  /**
   * Filtra i pazienti localmente
   * @param filterFn Funzione di filtro
   * @returns Array di pazienti filtrati
   */
  const filterPatients = (filterFn: (patient: IPatient) => boolean): IPatient[] => {
    return patients.filter(filterFn);
  };

  /**
   * Ottiene statistiche sui pazienti
   * @returns Oggetto con statistiche
   */
  const getPatientStats = () => {
    const total = patients.length;
    const withEmail = patients.filter((p: IPatient) => p.email).length;
    const withPhone = patients.filter((p: IPatient) => p.phone).length;
    const withFiscalCode = patients.filter((p: IPatient) => p.fiscalCode).length;

    return {
      total,
      withEmail,
      withPhone,
      withFiscalCode,
      completionRate: total > 0 ? Math.round((withEmail + withPhone + withFiscalCode) / (total * 3) * 100) : 0,
    };
  };

  // Ritorna l'oggetto con tutti i dati e le funzioni
  return {
    // Dati
    patients,
    isLoading,
    error,
    
    // Operazioni CRUD
    createPatient: handleCreatePatient,
    updatePatient: handleUpdatePatient,
    deletePatient: handleDeletePatient,
    searchPatients: handleSearchPatients,
    
    // Utilità
    refresh: handleRefresh,
    findById: findPatientById,
    filter: filterPatients,
    getStats: getPatientStats,
    clearError,
    
    // Stato
    hasPatients: patients.length > 0,
    isEmpty: patients.length === 0 && !isLoading,
  };
};

export default usePatients;
