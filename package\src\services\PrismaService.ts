/**
 * Servizio base per la gestione del database con Prisma
 * 
 * Questo servizio fornisce l'istanza del client Prisma e metodi di utilità
 * per la gestione delle operazioni del database nel gestionale dentisti.
 * 
 * Caratteristiche:
 * - Singleton pattern per garantire una sola istanza del client
 * - Gestione degli errori centralizzata
 * - Logging delle operazioni per debugging
 * - Supporto per transazioni
 */

import { PrismaClient } from '../../generated/prisma';

/**
 * Classe singleton per la gestione del client Prisma
 */
class PrismaService {
  private static instance: PrismaService;
  private prisma: PrismaClient;

  /**
   * Costruttore privato per implementare il pattern singleton
   */
  private constructor() {
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'], // Logging per debugging
    });
  }

  /**
   * Ottiene l'istanza singleton del servizio Prisma
   * @returns Istanza del servizio Prisma
   */
  public static getInstance(): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService();
    }
    return PrismaService.instance;
  }

  /**
   * Ottiene il client Prisma
   * @returns Client Prisma per operazioni database
   */
  public getClient(): PrismaClient {
    return this.prisma;
  }

  /**
   * Connette al database
   * Utile per verificare la connessione all'avvio dell'applicazione
   */
  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      console.log('✅ Database connesso con successo');
    } catch (error) {
      console.error('❌ Errore connessione database:', error);
      throw error;
    }
  }

  /**
   * Disconnette dal database
   * Importante chiamare questo metodo quando l'applicazione si chiude
   */
  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      console.log('✅ Database disconnesso con successo');
    } catch (error) {
      console.error('❌ Errore disconnessione database:', error);
      throw error;
    }
  }

  /**
   * Esegue una transazione
   * Utile per operazioni che coinvolgono più tabelle
   * 
   * @param operations Funzione che contiene le operazioni da eseguire in transazione
   * @returns Risultato della transazione
   */
  public async transaction<T>(
    operations: (prisma: PrismaClient) => Promise<T>
  ): Promise<T> {
    try {
      return await this.prisma.$transaction(async (prisma) => {
        return await operations(prisma);
      });
    } catch (error) {
      console.error('❌ Errore durante la transazione:', error);
      throw error;
    }
  }

  /**
   * Verifica lo stato di salute del database
   * @returns True se il database è raggiungibile, false altrimenti
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('❌ Health check fallito:', error);
      return false;
    }
  }

  /**
   * Gestisce gli errori del database in modo centralizzato
   * @param error Errore da gestire
   * @param operation Nome dell'operazione che ha causato l'errore
   */
  public handleError(error: any, operation: string): never {
    console.error(`❌ Errore durante ${operation}:`, error);
    
    // Gestione errori specifici di Prisma
    if (error.code === 'P2002') {
      throw new Error(`Violazione vincolo di unicità durante ${operation}`);
    } else if (error.code === 'P2025') {
      throw new Error(`Record non trovato durante ${operation}`);
    } else if (error.code === 'P2003') {
      throw new Error(`Violazione vincolo di chiave esterna durante ${operation}`);
    } else {
      throw new Error(`Errore database durante ${operation}: ${error.message}`);
    }
  }
}

// Esporta l'istanza singleton
export const prismaService = PrismaService.getInstance();
export default prismaService;
