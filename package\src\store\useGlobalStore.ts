import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { IPatient, ICreatePatient } from '../types/patients/IPatient';
import patientService from '../services/PatientService';

// Tipi globali aggiornati per compatibilità con Prisma

export interface IProduct {
  id: number;
  name: string;
  // ...altri campi prodotto
}

export interface ISession {
  userId: string;
  role: string;
  // ...altri campi sessione
}

// Slice: Pazienti con integrazione Prisma
interface PatientStore {
  patients: IPatient[];
  isLoading: boolean;
  error: string | null;

  // Operazioni CRUD con database
  loadPatients: () => Promise<void>;
  createPatient: (patientData: ICreatePatient) => Promise<IPatient | null>;
  updatePatient: (id: string, patientData: Partial<ICreatePatient>) => Promise<IPatient | null>;
  deletePatient: (id: string) => Promise<boolean>;
  searchPatients: (query: string) => Promise<IPatient[]>;

  // Operazioni locali per compatibilità
  setPatients: (patients: IPatient[]) => void;
  addPatientLocal: (patient: IPatient) => void;
  clearError: () => void;
}

const createPatientSlice = (set: any, get: any): PatientStore => ({
  patients: [],
  isLoading: false,
  error: null,

  // Carica tutti i pazienti dal database
  loadPatients: async () => {
    set({ isLoading: true, error: null });
    try {
      const result = await patientService.getPatients();
      set({ patients: result.patients, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      console.error('Errore caricamento pazienti:', error);
    }
  },

  // Crea un nuovo paziente nel database
  createPatient: async (patientData: ICreatePatient) => {
    set({ isLoading: true, error: null });
    try {
      const newPatient = await patientService.createPatient({
        firstName: patientData.firstName,
        lastName: patientData.lastName,
        email: patientData.email || null,
        phone: patientData.phone || null,
        dateOfBirth: patientData.dateOfBirth ? new Date(patientData.dateOfBirth) : null,
        address: patientData.address || null,
        city: patientData.city || null,
        postalCode: patientData.postalCode || null,
        fiscalCode: patientData.fiscalCode || null,
        notes: patientData.notes || null,
      });

      // Aggiorna la lista locale
      const currentPatients = get().patients;
      set({
        patients: [...currentPatients, newPatient],
        isLoading: false
      });

      return newPatient;
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      console.error('Errore creazione paziente:', error);
      return null;
    }
  },

  // Aggiorna un paziente nel database
  updatePatient: async (id: string, patientData: Partial<ICreatePatient>) => {
    set({ isLoading: true, error: null });
    try {
      const updateData: any = {};
      if (patientData.firstName !== undefined) updateData.firstName = patientData.firstName;
      if (patientData.lastName !== undefined) updateData.lastName = patientData.lastName;
      if (patientData.email !== undefined) updateData.email = patientData.email || null;
      if (patientData.phone !== undefined) updateData.phone = patientData.phone || null;
      if (patientData.dateOfBirth !== undefined) updateData.dateOfBirth = patientData.dateOfBirth ? new Date(patientData.dateOfBirth) : null;
      if (patientData.address !== undefined) updateData.address = patientData.address || null;
      if (patientData.city !== undefined) updateData.city = patientData.city || null;
      if (patientData.postalCode !== undefined) updateData.postalCode = patientData.postalCode || null;
      if (patientData.fiscalCode !== undefined) updateData.fiscalCode = patientData.fiscalCode || null;
      if (patientData.notes !== undefined) updateData.notes = patientData.notes || null;

      const updatedPatient = await patientService.updatePatient(id, updateData);

      // Aggiorna la lista locale
      const currentPatients = get().patients;
      const updatedPatients = currentPatients.map((p: IPatient) =>
        p.id === id ? updatedPatient : p
      );
      set({
        patients: updatedPatients,
        isLoading: false
      });

      return updatedPatient;
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      console.error('Errore aggiornamento paziente:', error);
      return null;
    }
  },

  // Elimina un paziente dal database
  deletePatient: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const success = await patientService.deletePatient(id);

      if (success) {
        // Rimuovi dalla lista locale
        const currentPatients = get().patients;
        const filteredPatients = currentPatients.filter((p: IPatient) => p.id !== id);
        set({
          patients: filteredPatients,
          isLoading: false
        });
      }

      return success;
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      console.error('Errore eliminazione paziente:', error);
      return false;
    }
  },

  // Cerca pazienti nel database
  searchPatients: async (query: string) => {
    try {
      const results = await patientService.searchPatients(query);
      return results;
    } catch (error: any) {
      console.error('Errore ricerca pazienti:', error);
      return [];
    }
  },

  // Operazioni locali per compatibilità con codice esistente
  setPatients: (patients: IPatient[]) => set({ patients }),
  addPatientLocal: (patient: IPatient) => {
    const currentPatients = get().patients;
    set({ patients: [...currentPatients, patient] });
  },
  clearError: () => set({ error: null }),
});

// Slice: Inventario
interface InventoryStore {
  inventory: IProduct[];
  addProduct: (product: IProduct) => void;
  // ...altri metodi
}

const createInventorySlice = (set: any): InventoryStore => ({
  inventory: [],
  addProduct: (product) => set((state: any) => ({ inventory: [...state.inventory, product] })),
});

// Slice: Sessione
interface SessionStore {
  session: ISession | null;
  setSession: (session: ISession) => void;
  clearSession: () => void;
}

const createSessionSlice = (set: any): SessionStore => ({
  session: null,
  setSession: (session) => set({ session }),
  clearSession: () => set({ session: null }),
});

// Store globale
export const useGlobalStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...createPatientSlice(set, get),
        ...createInventorySlice(set),
        ...createSessionSlice(set),
      }),
      { name: 'dental-crm' }
    )
  )
);
