import { <PERSON>, Badge, But<PERSON>, Select, TextInput, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "flowbite-react";
import { Icon } from "@iconify/react";
import SimpleBar from "simplebar-react";
import { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { HiSearch } from "react-icons/hi";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useInvoices } from '../../hooks/useInvoices';
import { usePatients } from '../../hooks/usePatients';
import {
  InvoiceStatus,
  IInvoiceFilters,
  getStatusLabel,
  getStatusColor,
  formatCurrency,
  formatInvoiceDate
} from '../../types/invoices/IInvoice';

// Componente principale per la gestione delle fatture

// Componente per il template di stampa dell'elenco fatture
const InvoiceListPrintTemplate = ({
  invoices,
  period
}: {
  invoices: typeof BillingData,
  period: string
}) => {
  return (
    <div id="print-template" className="p-8 max-w-4xl mx-auto bg-white text-black">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold">ELENCO FATTURE</h1>
        <p className="text-sm text-gray-600">MOKO SOSTANZA Dental CRM</p>
        <p className="text-sm text-gray-600 mt-2">Periodo: {period}</p>
        <p className="text-sm text-gray-600">Generato il {new Date().toLocaleDateString('it-IT')}</p>
      </div>

      {/* Tabella fatture */}
      <div className="mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 text-left">Paziente</th>
              <th className="border p-2 text-left">Data</th>
              <th className="border p-2 text-left">Numero Fattura</th>
              <th className="border p-2 text-right">Importo</th>
              <th className="border p-2 text-center">Stato</th>
            </tr>
          </thead>
          <tbody>
            {invoices.length > 0 ? (
              invoices.map((bill) => (
                <tr key={bill.id}>
                  <td className="border p-2">{bill.patient}</td>
                  <td className="border p-2">{bill.date}</td>
                  <td className="border p-2">{bill.invoice}</td>
                  <td className="border p-2 text-right">{bill.amount}</td>
                  <td className="border p-2 text-center">
                    <span className={bill.status === "Pagato" ? "text-green-600 font-bold" : "text-orange-500 font-bold"}>
                      {bill.status}
                    </span>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="border p-2 text-center">Nessuna fattura trovata</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Riepilogo */}
      <div className="mb-8">
        <div className="flex justify-between border-t border-gray-300 pt-4">
          <div>
            <p><strong>Totale fatture:</strong> {invoices.length}</p>
          </div>
          <div>
            <p><strong>Totale importo:</strong> {
              invoices.reduce((total, bill) => {
                const amount = parseFloat(bill.amount.replace('€', '').replace(',', '.').trim());
                return total + (isNaN(amount) ? 0 : amount);
              }, 0).toFixed(2).replace('.', ',')
            } €</p>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center text-sm text-gray-500">
        <p>MOKO SOSTANZA Dental CRM - Gestionale per Dentisti</p>
        <p>Documento generato automaticamente - {new Date().toLocaleDateString('it-IT')}</p>
      </div>
    </div>
  );
};

const Billing = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Hook per fatture e pazienti
  const {
    invoices,
    isLoading,
    error,
    clearError,
    getStats,
    deleteInvoice
  } = useInvoices();

  const { findById: findPatientById } = usePatients();

  // Stato locale per filtri e ricerca
  const [selectedPeriod, setSelectedPeriod] = useState("Questo Mese");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<InvoiceStatus | "">("");
  const [filteredInvoices, setFilteredInvoices] = useState(invoices);
  const [showPrintTemplate, setShowPrintTemplate] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const printTemplateRef = useRef<HTMLDivElement>(null);
  const pdfTemplateRef = useRef<HTMLDivElement>(null);

  // Verifica se siamo nella pagina di ricerca
  const isSearchPage = location.pathname === "/billing/search";

  // Imposta il focus sul campo di ricerca quando si accede alla pagina di ricerca
  useEffect(() => {
    if (isSearchPage) {
      const searchInput = document.getElementById('search-invoice');
      if (searchInput) {
        searchInput.focus();
      }
    }
  }, [isSearchPage]);

  // Filtra le fatture in base ai criteri selezionati
  useEffect(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    let filtered = [...invoices];

    // Filtro per periodo
    switch (selectedPeriod) {
      case "Oggi":
        filtered = filtered.filter(inv => {
          const invDate = new Date(inv.issueDate);
          invDate.setHours(0, 0, 0, 0);
          return invDate.getTime() === today.getTime();
        });
        break;
      case "Questa Settimana":
        filtered = filtered.filter(inv => inv.issueDate >= startOfWeek);
        break;
      case "Questo Mese":
        filtered = filtered.filter(inv => inv.issueDate >= startOfMonth);
        break;
      case "Quest'Anno":
        filtered = filtered.filter(inv => inv.issueDate >= startOfYear);
        break;
    }

    // Filtro per stato
    if (statusFilter) {
      filtered = filtered.filter(inv => inv.status === statusFilter);
    }

    // Filtro per ricerca
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(inv =>
        inv.invoiceNumber.toLowerCase().includes(term) ||
        inv.description?.toLowerCase().includes(term) ||
        inv.patient?.firstName.toLowerCase().includes(term) ||
        inv.patient?.lastName.toLowerCase().includes(term) ||
        inv.notes?.toLowerCase().includes(term)
      );
    }

    setFilteredInvoices(filtered);
  }, [invoices, selectedPeriod, statusFilter, searchTerm]);

  // Gestori eventi
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedPeriod(e.target.value);
  };

  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value as InvoiceStatus | "");
  };

  const handleNewInvoice = () => {
    navigate('/billing/new');
  };

  const handleEditInvoice = (invoiceId: string) => {
    navigate(`/billing/edit/${invoiceId}`);
  };

  const handleDeleteInvoice = async (invoiceId: string) => {
    if (window.confirm('Sei sicuro di voler eliminare questa fattura?')) {
      await deleteInvoice(invoiceId);
    }
  };

  // Funzione per stampare l'elenco delle fatture
  const handlePrintInvoiceList = () => {
    setShowPrintTemplate(true);
    setTimeout(() => {
      window.print();
      setTimeout(() => {
        setShowPrintTemplate(false);
      }, 500);
    }, 300);
  };

  // Funzione per generare e scaricare il PDF dell'elenco fatture
  const handleDownloadPDF = async () => {
    // Mostra l'indicatore di caricamento
    setIsGeneratingPDF(true);

    try {
      // Assicuriamoci che il template PDF sia visibile
      if (!pdfTemplateRef.current) {
        console.error('Riferimento al template PDF non disponibile');
        setIsGeneratingPDF(false);
        return;
      }

      // Utilizziamo una versione semplificata di html2canvas + jsPDF
      const element = pdfTemplateRef.current;

      // Creiamo il canvas dall'elemento HTML
      const canvas = await html2canvas(element, {
        scale: 2, // Alta qualità
        useCORS: true,
        backgroundColor: '#ffffff',
        allowTaint: true,
        scrollX: 0,
        scrollY: 0,
        windowWidth: element.scrollWidth,
        windowHeight: element.scrollHeight
      });

      // Creiamo il documento PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Dimensioni A4 in mm
      const pageWidth = 210;
      const pageHeight = 297;

      // Margini
      const margin = 10;

      // Dimensioni effettive dell'area di stampa
      const printWidth = pageWidth - (margin * 2);

      // Calcola le dimensioni dell'immagine nel PDF
      const imgWidth = printWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Aggiungiamo l'immagine al PDF
      // Utilizziamo un metodo più semplice per la suddivisione in pagine

      // Altezza massima per pagina
      const maxHeight = pageHeight - (margin * 2);

      // Numero di pagine necessarie
      const pageCount = Math.ceil(imgHeight / maxHeight);

      // Per ogni pagina
      for (let i = 0; i < pageCount; i++) {
        // Se non è la prima pagina, aggiungi una nuova pagina
        if (i > 0) {
          pdf.addPage();
        }

        // Calcola la posizione di inizio nel canvas
        const sourceY = (i * maxHeight / imgHeight) * canvas.height;

        // Calcola l'altezza della porzione da includere
        const sourceHeight = Math.min(
          canvas.height - sourceY,
          (maxHeight / imgHeight) * canvas.height
        );

        // Crea un canvas temporaneo per la porzione corrente
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = canvas.width;
        tempCanvas.height = sourceHeight;

        // Disegna la porzione corrente sul canvas temporaneo
        const ctx = tempCanvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(
            canvas,
            0, sourceY, canvas.width, sourceHeight,
            0, 0, tempCanvas.width, tempCanvas.height
          );

          // Converti il canvas temporaneo in immagine
          const imgData = tempCanvas.toDataURL('image/jpeg', 1.0);

          // Aggiungi l'immagine al PDF
          pdf.addImage(
            imgData,
            'JPEG',
            margin,
            margin,
            printWidth,
            (sourceHeight * printWidth) / canvas.width
          );
        }
      }

      // Aggiungi metadati al PDF
      pdf.setProperties({
        title: `Elenco Fatture - ${selectedPeriod}`,
        subject: 'Elenco Fatture',
        author: 'MOKO SOSTANZA Dental CRM',
        keywords: 'fatture, dentista, elenco',
        creator: 'MOKO SOSTANZA Dental CRM'
      });

      // Genera il nome del file
      const fileName = `Elenco_Fatture_${selectedPeriod.replace(/\s+/g, '_')}.pdf`;

      // Scarica il PDF
      pdf.save(fileName);

    } catch (error) {
      console.error('Errore durante la generazione del PDF:', error);
    } finally {
      // Nascondi l'indicatore di caricamento
      setIsGeneratingPDF(false);
    }
  };

  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <h5 className="card-title">Gestione Fatturazione</h5>
            <div className="flex gap-2">
              <Tooltip content="Stampa l'elenco delle fatture">
                <Button color="light" className="flex items-center gap-2 no-print" onClick={handlePrintInvoiceList}>
                  <Icon icon="solar:printer-outline" height={20} />
                  <span className="hidden sm:inline">Stampa Elenco</span>
                  <span className="sm:hidden">Stampa</span>
                </Button>
              </Tooltip>
              <Tooltip content="Scarica l'elenco delle fatture in formato PDF">
                <Button
                  color="light"
                  onClick={handleDownloadPDF}
                  className="flex items-center gap-2 no-print"
                  disabled={isGeneratingPDF}
                >
                  {isGeneratingPDF ? (
                    <>
                      <div className="animate-spin h-5 w-5 border-2 border-gray-500 border-t-transparent rounded-full"></div>
                      <span className="hidden sm:inline">Generazione...</span>
                      <span className="sm:hidden">PDF...</span>
                    </>
                  ) : (
                    <>
                      <Icon icon="solar:file-download-outline" height={20} />
                      <span className="hidden sm:inline">Scarica PDF</span>
                      <span className="sm:hidden">PDF</span>
                    </>
                  )}
                </Button>
              </Tooltip>
              <Button color="primary" className="flex items-center gap-2" as={Link} to="/billing/new">
                <Icon icon="solar:add-circle-outline" height={20} />
                <span className="hidden sm:inline">Nuova Fattura</span>
                <span className="sm:hidden">Nuova</span>
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 no-print">
            <div className="relative">
              <TextInput
                id="search-invoice"
                type="text"
                placeholder="Cerca fattura..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-full"
                icon={HiSearch}
              />
            </div>
            <Select
              id="period-filter"
              className="w-full"
              value={selectedPeriod}
              onChange={handleSelectChange}
              required
            >
              <option value="Questo Mese">Questo Mese</option>
              <option value="Mese Scorso">Mese Scorso</option>
              <option value="Questo Trimestre">Questo Trimestre</option>
              <option value="Quest'Anno">Quest'Anno</option>
            </Select>
          </div>

          {/* Titolo per la stampa */}
          <div className="hidden print:block print:mb-4">
            <h2 className="text-2xl font-bold text-center">Elenco Fatture</h2>
            <p className="text-center text-gray-500 mt-2">
              Periodo: {selectedPeriod}
            </p>
            <p className="text-center text-gray-500 mt-1">
              Generato il {new Date().toLocaleDateString('it-IT')}
            </p>
          </div>
        </div>

        {/* Stili per la stampa */}
        <style type="text/css" media="print">
          {`
            @media print {
              body * {
                visibility: hidden;
              }
              #print-template, #print-template * {
                visibility: visible;
              }
              #print-template {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                font-size: 12pt;
                line-height: 1.3;
              }
              table {
                page-break-inside: avoid;
                font-size: 10pt;
              }
              table tr {
                page-break-inside: avoid;
              }
              p {
                orphans: 3;
                widows: 3;
              }
              h1, h2, h3, h4 {
                page-break-after: avoid;
              }
              @page {
                size: A4;
                margin: 15mm;
              }
              .no-print {
                display: none !important;
              }
            }
          `}
        </style>

        {/* Template di stampa (nascosto normalmente, visibile solo durante la stampa) */}
        {showPrintTemplate && (
          <div className="fixed top-0 left-0 w-full h-0 overflow-hidden print:h-auto print:overflow-visible">
            <div ref={printTemplateRef} className="bg-white">
              <InvoiceListPrintTemplate invoices={filteredBilling} period={selectedPeriod} />
            </div>
          </div>
        )}

        {/* Template di stampa nascosto ma renderizzato per la generazione PDF */}
        <div className="fixed top-0 left-0 w-full opacity-0 pointer-events-none" style={{ zIndex: -9999 }}>
          <div id="pdf-template" ref={pdfTemplateRef} className="bg-white p-0 m-0">
            <InvoiceListPrintTemplate invoices={filteredBilling} period={selectedPeriod} />
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table hoverable className="table-auto w-full">
            <Table.Head>
              <Table.HeadCell className="p-4">Paziente</Table.HeadCell>
              <Table.HeadCell className="p-4">Data</Table.HeadCell>
              <Table.HeadCell className="p-4 hidden md:table-cell md:print:table-cell">Numero Fattura</Table.HeadCell>
              <Table.HeadCell className="p-4">Importo</Table.HeadCell>
              <Table.HeadCell className="p-4">Stato</Table.HeadCell>
              <Table.HeadCell className="p-4 no-print">Azioni</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y divide-border dark:divide-darkborder">
              {filteredBilling.length > 0 ? (
                filteredBilling.map((bill) => (
                <Table.Row key={bill.id}>
                  <Table.Cell className="p-4">
                    <div className="flex gap-2 items-center">
                      <div>
                        <h6 className="text-sm font-medium">{bill.patient}</h6>
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell className="p-4">
                    <p className="text-sm">{bill.date}</p>
                  </Table.Cell>
                  <Table.Cell className="p-4 hidden md:table-cell md:print:table-cell">
                    <p className="text-sm">{bill.invoice}</p>
                  </Table.Cell>
                  <Table.Cell className="p-4">
                    <p className="text-sm">{bill.amount}</p>
                  </Table.Cell>
                  <Table.Cell className="p-4">
                    <Badge
                      className={
                        bill.status === "Pagato"
                          ? "bg-lightsuccess text-success"
                          : "bg-lightwarning text-warning"
                      }
                    >
                      {bill.status}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell className="p-4 no-print">
                    <div className="flex gap-2">
                      <Button color="primary" size="xs" as={Link} to={`/billing/invoices/${bill.id}`}>
                        <Icon icon="solar:eye-outline" height={16} />
                      </Button>
                      <Button
                        color="secondary"
                        size="xs"
                        onClick={() => {
                          window.open(`/billing/invoices/${bill.id}?print=true`, '_blank');
                        }}
                      >
                        <Icon icon="solar:printer-outline" height={16} />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={6} className="text-center py-4">
                    <p className="text-gray-500">Nessuna fattura trovata</p>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </div>
      </div>
    </>
  );
};

export default Billing;
